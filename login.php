<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . BASE_URL . '/dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// Handle login form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFFromPost()) {
        $error_message = 'خطأ في التحقق من الأمان. حاول مرة أخرى.';
    } else {
        $username = sanitizeInput($_POST['username'] ?? '');
        $password = $_POST['password'] ?? '';
        
        if (empty($username) || empty($password)) {
            $error_message = 'جميع الحقول مطلوبة';
        } else {
            $result = authenticateUser($username, $password);
            
            if ($result['success']) {
                $success_message = $result['message'];
                // Redirect after successful login
                header('Location: ' . BASE_URL . '/dashboard.php');
                exit();
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

// Check for session timeout
if (isset($_GET['timeout'])) {
    $error_message = 'انتهت صلاحية الجلسة. يرجى تسجيل الدخول مرة أخرى.';
}

$page_title = $lang['login_title'];
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6 col-lg-5">
            <div class="card shadow-sm mt-5">
                <div class="card-header text-center bg-primary text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-box-arrow-in-right me-2"></i>
                        <?php echo $lang['login_title']; ?>
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Organization Logo/Name -->
                    <div class="text-center mb-4">
                        <h5 class="text-muted"><?php echo $lang['organization_name']; ?></h5>
                        <p class="small text-muted"><?php echo $lang['app_name']; ?></p>
                    </div>
                    
                    <!-- Error/Success Messages -->
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Login Form -->
                    <form method="POST" action="" novalidate>
                        <?php echo getCSRFTokenField(); ?>
                        
                        <div class="mb-3">
                            <label for="username" class="form-label">
                                <i class="bi bi-person me-1"></i>
                                <?php echo $lang['username']; ?> أو <?php echo $lang['email']; ?>
                            </label>
                            <input type="text" 
                                   class="form-control" 
                                   id="username" 
                                   name="username" 
                                   value="<?php echo isset($_POST['username']) ? preventXSS($_POST['username']) : ''; ?>"
                                   required 
                                   autocomplete="username"
                                   placeholder="أدخل اسم المستخدم أو البريد الإلكتروني">
                            <div class="invalid-feedback">
                                يرجى إدخال اسم المستخدم أو البريد الإلكتروني
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="password" class="form-label">
                                <i class="bi bi-lock me-1"></i>
                                <?php echo $lang['password']; ?>
                            </label>
                            <div class="input-group">
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       autocomplete="current-password"
                                       placeholder="أدخل كلمة المرور">
                                <button class="btn btn-outline-secondary" 
                                        type="button" 
                                        id="togglePassword"
                                        title="إظهار/إخفاء كلمة المرور">
                                    <i class="bi bi-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback">
                                يرجى إدخال كلمة المرور
                            </div>
                        </div>
                        
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                            <label class="form-check-label" for="remember_me">
                                <?php echo $lang['remember_me']; ?>
                            </label>
                        </div>
                        
                        <div class="d-grid">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-box-arrow-in-right me-2"></i>
                                <?php echo $lang['login']; ?>
                            </button>
                        </div>
                    </form>
                    
                    <!-- Additional Links -->
                    <div class="text-center mt-4">
                        <p class="mb-2">
                            <a href="<?php echo BASE_URL; ?>/forgot-password.php" class="text-decoration-none">
                                <i class="bi bi-question-circle me-1"></i>
                                <?php echo $lang['forgot_password']; ?>
                            </a>
                        </p>
                        <hr>
                        <p class="mb-0">
                            ليس لديك حساب؟ 
                            <a href="<?php echo BASE_URL; ?>/register.php" class="text-decoration-none">
                                <i class="bi bi-person-plus me-1"></i>
                                <?php echo $lang['register']; ?>
                            </a>
                        </p>
                    </div>
                </div>
            </div>
            
            <!-- Demo Credentials -->
            <div class="card mt-3">
                <div class="card-body">
                    <h6 class="card-title text-center">بيانات تجريبية للاختبار</h6>
                    <div class="row">
                        <div class="col-6">
                            <strong>مدير النظام:</strong><br>
                            <small>
                                المستخدم: admin<br>
                                كلمة المرور: admin123
                            </small>
                        </div>
                        <div class="col-6">
                            <strong>موظف:</strong><br>
                            <small>
                                قم بإنشاء حساب جديد<br>
                                من خلال التسجيل
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Toggle password visibility
document.getElementById('togglePassword').addEventListener('click', function() {
    const password = document.getElementById('password');
    const icon = this.querySelector('i');
    
    if (password.type === 'password') {
        password.type = 'text';
        icon.classList.remove('bi-eye');
        icon.classList.add('bi-eye-slash');
    } else {
        password.type = 'password';
        icon.classList.remove('bi-eye-slash');
        icon.classList.add('bi-eye');
    }
});

// Auto-focus on username field
document.getElementById('username').focus();

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
