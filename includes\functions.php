<?php
/**
 * Common Functions
 * Employee Leave Management System
 */

/**
 * Sanitize input data
 */
function sanitizeInput($data) {
    if (is_array($data)) {
        return array_map('sanitizeInput', $data);
    }
    return htmlspecialchars(trim($data), ENT_QUOTES, 'UTF-8');
}

/**
 * Validate email address
 */
function isValidEmail($email) {
    return filter_var($email, FILTER_VALIDATE_EMAIL) !== false;
}

/**
 * Generate random string
 */
function generateRandomString($length = 10) {
    return bin2hex(random_bytes($length / 2));
}

/**
 * Format date for display
 */
function formatDate($date, $format = 'Y-m-d') {
    if (empty($date)) return '';
    
    $dateObj = new DateTime($date);
    
    if (IS_RTL) {
        // Arabic date format
        $months_ar = [
            1 => 'يناير', 2 => 'فبراير', 3 => 'مارس', 4 => 'أبريل',
            5 => 'مايو', 6 => 'يونيو', 7 => 'يوليو', 8 => 'أغسطس',
            9 => 'سبتمبر', 10 => 'أكتوبر', 11 => 'نوفمبر', 12 => 'ديسمبر'
        ];
        
        $day = $dateObj->format('d');
        $month = $months_ar[(int)$dateObj->format('m')];
        $year = $dateObj->format('Y');
        
        return "$day $month $year";
    }
    
    return $dateObj->format($format);
}

/**
 * Calculate working days between two dates
 */
function calculateWorkingDays($startDate, $endDate) {
    $start = new DateTime($startDate);
    $end = new DateTime($endDate);
    $end->modify('+1 day'); // Include end date
    
    $interval = new DateInterval('P1D');
    $period = new DatePeriod($start, $interval, $end);
    
    $workingDays = 0;
    foreach ($period as $date) {
        $dayOfWeek = $date->format('N'); // 1 (Monday) to 7 (Sunday)
        if ($dayOfWeek < 6) { // Monday to Friday
            $workingDays++;
        }
    }
    
    return $workingDays;
}

/**
 * Generate leave request number
 */
function generateLeaveRequestNumber() {
    $year = date('Y');
    $month = date('m');
    $random = str_pad(mt_rand(1, 9999), 4, '0', STR_PAD_LEFT);
    
    return "LR{$year}{$month}{$random}";
}

/**
 * Get leave status badge HTML
 */
function getStatusBadge($status) {
    $badges = [
        'pending' => '<span class="badge bg-warning text-dark">قيد الانتظار</span>',
        'approved' => '<span class="badge bg-success">موافق عليها</span>',
        'rejected' => '<span class="badge bg-danger">مرفوضة</span>',
        'cancelled' => '<span class="badge bg-secondary">ملغية</span>'
    ];
    
    return $badges[$status] ?? '<span class="badge bg-secondary">غير محدد</span>';
}

/**
 * Log audit trail
 */
function logAudit($action, $table_name, $record_id = null, $old_values = null, $new_values = null) {
    global $database;
    
    $user_id = $_SESSION['user_id'] ?? null;
    $ip_address = $_SERVER['REMOTE_ADDR'] ?? null;
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? null;
    
    $sql = "INSERT INTO audit_logs (user_id, action, table_name, record_id, old_values, new_values, ip_address, user_agent) 
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
    
    $params = [
        $user_id,
        $action,
        $table_name,
        $record_id,
        $old_values ? json_encode($old_values) : null,
        $new_values ? json_encode($new_values) : null,
        $ip_address,
        $user_agent
    ];
    
    try {
        $database->execute($sql, $params);
    } catch (Exception $e) {
        error_log("Audit log failed: " . $e->getMessage());
    }
}

/**
 * Send email notification
 */
function sendEmail($to, $subject, $message, $isHTML = true) {
    // This is a placeholder for email functionality
    // You can integrate with PHPMailer or similar library
    
    // For now, just log the email
    error_log("Email to: $to, Subject: $subject, Message: $message");
    
    return true; // Return true for testing
}

/**
 * Get user's leave balance for current month
 */
function getUserLeaveBalance($user_id, $year = null, $month = null) {
    global $database;
    
    if (!$year) $year = date('Y');
    if (!$month) $month = date('m');
    
    $sql = "SELECT * FROM leave_balances WHERE user_id = ? AND year = ? AND month = ?";
    $balance = $database->fetchOne($sql, [$user_id, $year, $month]);
    
    if (!$balance) {
        // Create balance record if it doesn't exist
        $sql = "INSERT INTO leave_balances (user_id, year, month, monthly_allocation, remaining_days, total_balance) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $monthly_allocation = MONTHLY_LEAVE_ALLOCATION;
        $database->execute($sql, [$user_id, $year, $month, $monthly_allocation, $monthly_allocation, $monthly_allocation]);
        
        // Get the newly created record
        $balance = $database->fetchOne("SELECT * FROM leave_balances WHERE user_id = ? AND year = ? AND month = ?", 
                                     [$user_id, $year, $month]);
    }
    
    return $balance;
}

/**
 * Update user's leave balance
 */
function updateLeaveBalance($user_id, $days_used, $year = null, $month = null) {
    global $database;
    
    if (!$year) $year = date('Y');
    if (!$month) $month = date('m');
    
    $balance = getUserLeaveBalance($user_id, $year, $month);
    
    $new_used_days = $balance['used_days'] + $days_used;
    $new_remaining_days = $balance['total_balance'] - $new_used_days;
    
    $sql = "UPDATE leave_balances 
            SET used_days = ?, remaining_days = ? 
            WHERE user_id = ? AND year = ? AND month = ?";
    
    return $database->execute($sql, [$new_used_days, $new_remaining_days, $user_id, $year, $month]);
}

/**
 * Check if user has sufficient leave balance
 */
function hasSufficientBalance($user_id, $requested_days) {
    $balance = getUserLeaveBalance($user_id);
    return $balance['remaining_days'] >= $requested_days;
}

/**
 * Get departments list
 */
function getDepartments($active_only = true) {
    global $database;
    
    $sql = "SELECT * FROM departments";
    if ($active_only) {
        $sql .= " WHERE is_active = 1";
    }
    $sql .= " ORDER BY dept_name";
    
    return $database->fetchAll($sql);
}

/**
 * Get users by department
 */
function getUsersByDepartment($department_id, $exclude_user_id = null) {
    global $database;
    
    $sql = "SELECT * FROM users WHERE department_id = ? AND is_active = 1";
    $params = [$department_id];
    
    if ($exclude_user_id) {
        $sql .= " AND id != ?";
        $params[] = $exclude_user_id;
    }
    
    $sql .= " ORDER BY full_name";
    
    return $database->fetchAll($sql, $params);
}

/**
 * Redirect with message
 */
function redirectWithMessage($url, $message, $type = 'success') {
    $_SESSION['flash_message'] = $message;
    $_SESSION['flash_type'] = $type;
    header("Location: $url");
    exit();
}

/**
 * Display flash message
 */
function displayFlashMessage() {
    if (isset($_SESSION['flash_message'])) {
        $message = $_SESSION['flash_message'];
        $type = $_SESSION['flash_type'] ?? 'info';
        
        echo "<div class='alert alert-{$type} alert-dismissible fade show' role='alert'>
                {$message}
                <button type='button' class='btn-close' data-bs-dismiss='alert'></button>
              </div>";
        
        unset($_SESSION['flash_message']);
        unset($_SESSION['flash_type']);
    }
}

/**
 * Get current page name
 */
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

/**
 * Check if current page is active
 */
function isActivePage($page) {
    return getCurrentPage() === $page ? 'active' : '';
}
?>
