<?php
/**
 * Security Functions
 * Employee Leave Management System
 */

/**
 * Generate CSRF token
 */
function generateCSRFToken() {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
    }
    return $_SESSION[CSRF_TOKEN_NAME];
}

/**
 * Validate CSRF token
 */
function validateCSRFToken($token) {
    if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
        return false;
    }
    
    return hash_equals($_SESSION[CSRF_TOKEN_NAME], $token);
}

/**
 * Get CSRF token input field
 */
function getCSRFTokenField() {
    $token = generateCSRFToken();
    return "<input type='hidden' name='" . CSRF_TOKEN_NAME . "' value='{$token}'>";
}

/**
 * Validate CSRF token from POST data
 */
function validateCSRFFromPost() {
    if (!isset($_POST[CSRF_TOKEN_NAME])) {
        return false;
    }
    
    return validateCSRFToken($_POST[CSRF_TOKEN_NAME]);
}

/**
 * Prevent XSS attacks
 */
function preventXSS($data) {
    if (is_array($data)) {
        return array_map('preventXSS', $data);
    }
    
    return htmlspecialchars($data, ENT_QUOTES | ENT_HTML5, 'UTF-8');
}

/**
 * Validate input against SQL injection
 */
function validateInput($input, $type = 'string', $max_length = null) {
    // Remove any null bytes
    $input = str_replace(chr(0), '', $input);
    
    // Trim whitespace
    $input = trim($input);
    
    // Check length
    if ($max_length && strlen($input) > $max_length) {
        return false;
    }
    
    switch ($type) {
        case 'email':
            return filter_var($input, FILTER_VALIDATE_EMAIL);
            
        case 'int':
            return filter_var($input, FILTER_VALIDATE_INT);
            
        case 'float':
            return filter_var($input, FILTER_VALIDATE_FLOAT);
            
        case 'date':
            $date = DateTime::createFromFormat('Y-m-d', $input);
            return $date && $date->format('Y-m-d') === $input;
            
        case 'username':
            return preg_match('/^[a-zA-Z0-9_]{3,20}$/', $input);
            
        case 'employee_id':
            return preg_match('/^[A-Z0-9]{3,20}$/', $input);
            
        case 'phone':
            return preg_match('/^[\+]?[0-9\s\-\(\)]{7,20}$/', $input);
            
        case 'string':
        default:
            // Basic string validation - no script tags
            return !preg_match('/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/mi', $input);
    }
}

/**
 * Rate limiting for login attempts
 */
function checkLoginAttempts($identifier) {
    $key = 'login_attempts_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = [
            'count' => 0,
            'last_attempt' => 0
        ];
    }
    
    $attempts = $_SESSION[$key];
    
    // Reset attempts if lockout time has passed
    if ($attempts['last_attempt'] && (time() - $attempts['last_attempt']) > LOGIN_LOCKOUT_TIME) {
        $_SESSION[$key] = [
            'count' => 0,
            'last_attempt' => 0
        ];
        return true;
    }
    
    // Check if user is locked out
    if ($attempts['count'] >= MAX_LOGIN_ATTEMPTS) {
        return false;
    }
    
    return true;
}

/**
 * Record failed login attempt
 */
function recordFailedLogin($identifier) {
    $key = 'login_attempts_' . md5($identifier);
    
    if (!isset($_SESSION[$key])) {
        $_SESSION[$key] = [
            'count' => 0,
            'last_attempt' => 0
        ];
    }
    
    $_SESSION[$key]['count']++;
    $_SESSION[$key]['last_attempt'] = time();
}

/**
 * Reset login attempts
 */
function resetLoginAttempts($identifier) {
    $key = 'login_attempts_' . md5($identifier);
    unset($_SESSION[$key]);
}

/**
 * Secure password hashing
 */
function hashPassword($password) {
    return password_hash($password, PASSWORD_DEFAULT);
}

/**
 * Verify password
 */
function verifyPassword($password, $hash) {
    return password_verify($password, $hash);
}

/**
 * Generate secure random password
 */
function generateSecurePassword($length = 12) {
    $chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $chars[random_int(0, strlen($chars) - 1)];
    }
    
    return $password;
}

/**
 * Validate password strength
 */
function validatePasswordStrength($password) {
    $errors = [];
    
    if (strlen($password) < 8) {
        $errors[] = 'كلمة المرور يجب أن تكون 8 أحرف على الأقل';
    }
    
    if (!preg_match('/[A-Z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف كبير واحد على الأقل';
    }
    
    if (!preg_match('/[a-z]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على حرف صغير واحد على الأقل';
    }
    
    if (!preg_match('/[0-9]/', $password)) {
        $errors[] = 'كلمة المرور يجب أن تحتوي على رقم واحد على الأقل';
    }
    
    return $errors;
}

/**
 * Sanitize file upload
 */
function sanitizeFileUpload($file) {
    if (!isset($file['tmp_name']) || !is_uploaded_file($file['tmp_name'])) {
        return false;
    }
    
    // Check file size
    if ($file['size'] > MAX_FILE_SIZE) {
        return false;
    }
    
    // Get file extension
    $extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    // Check allowed extensions
    if (!in_array($extension, ALLOWED_FILE_TYPES)) {
        return false;
    }
    
    // Generate secure filename
    $filename = uniqid() . '_' . time() . '.' . $extension;
    
    return [
        'original_name' => $file['name'],
        'filename' => $filename,
        'extension' => $extension,
        'size' => $file['size'],
        'tmp_name' => $file['tmp_name']
    ];
}

/**
 * Log security event
 */
function logSecurityEvent($event, $details = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'user_id' => $_SESSION['user_id'] ?? null,
        'details' => $details
    ];
    
    error_log("SECURITY EVENT: " . json_encode($log_entry));
}

/**
 * Check for suspicious activity
 */
function checkSuspiciousActivity() {
    // Check for common attack patterns in request
    $suspicious_patterns = [
        '/union.*select/i',
        '/script.*alert/i',
        '/javascript:/i',
        '/vbscript:/i',
        '/onload=/i',
        '/onerror=/i'
    ];
    
    $request_data = array_merge($_GET, $_POST, $_COOKIE);
    
    foreach ($request_data as $key => $value) {
        if (is_string($value)) {
            foreach ($suspicious_patterns as $pattern) {
                if (preg_match($pattern, $value)) {
                    logSecurityEvent('suspicious_input', [
                        'key' => $key,
                        'value' => substr($value, 0, 100),
                        'pattern' => $pattern
                    ]);
                    return true;
                }
            }
        }
    }
    
    return false;
}

/**
 * Block suspicious requests
 */
function blockSuspiciousRequests() {
    if (checkSuspiciousActivity()) {
        http_response_code(403);
        die('Access Denied');
    }
}

// Auto-check for suspicious activity on every request
blockSuspiciousRequests();
?>
