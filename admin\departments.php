<?php
require_once '../config/config.php';
requireAdmin();

$page_title = 'إدارة الأقسام';

// Handle form submissions
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFFromPost()) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'خطأ في التحقق من الأمان', 'danger');
    }
    
    $action = $_POST['action'] ?? '';
    
    if ($action === 'add') {
        $data = [
            'dept_name' => sanitizeInput($_POST['dept_name'] ?? ''),
            'dept_name_ar' => sanitizeInput($_POST['dept_name_ar'] ?? ''),
            'dept_code' => sanitizeInput($_POST['dept_code'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'head_of_department' => sanitizeInput($_POST['head_of_department'] ?? '')
        ];
        
        if (empty($data['dept_name']) || empty($data['dept_name_ar']) || empty($data['dept_code'])) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'جميع الحقول المطلوبة يجب ملؤها', 'danger');
        }
        
        // Check for duplicate department code
        $existing = $database->fetchOne("SELECT id FROM departments WHERE dept_code = ?", [$data['dept_code']]);
        if ($existing) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'رمز القسم مستخدم بالفعل', 'danger');
        }
        
        $sql = "INSERT INTO departments (dept_name, dept_name_ar, dept_code, description, head_of_department) 
                VALUES (?, ?, ?, ?, ?)";
        
        try {
            $dept_id = $database->insert($sql, array_values($data));
            logAudit('create', 'departments', $dept_id, null, $data);
            redirectWithMessage($_SERVER['PHP_SELF'], 'تم إضافة القسم بنجاح', 'success');
        } catch (Exception $e) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'حدث خطأ أثناء إضافة القسم', 'danger');
        }
        
    } elseif ($action === 'edit') {
        $dept_id = (int)($_POST['dept_id'] ?? 0);
        $data = [
            'dept_name' => sanitizeInput($_POST['dept_name'] ?? ''),
            'dept_name_ar' => sanitizeInput($_POST['dept_name_ar'] ?? ''),
            'dept_code' => sanitizeInput($_POST['dept_code'] ?? ''),
            'description' => sanitizeInput($_POST['description'] ?? ''),
            'head_of_department' => sanitizeInput($_POST['head_of_department'] ?? '')
        ];
        
        if (empty($data['dept_name']) || empty($data['dept_name_ar']) || empty($data['dept_code'])) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'جميع الحقول المطلوبة يجب ملؤها', 'danger');
        }
        
        // Check for duplicate department code (excluding current department)
        $existing = $database->fetchOne("SELECT id FROM departments WHERE dept_code = ? AND id != ?", [$data['dept_code'], $dept_id]);
        if ($existing) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'رمز القسم مستخدم بالفعل', 'danger');
        }
        
        // Get old values for audit
        $old_data = $database->fetchOne("SELECT * FROM departments WHERE id = ?", [$dept_id]);
        
        $sql = "UPDATE departments SET dept_name = ?, dept_name_ar = ?, dept_code = ?, description = ?, head_of_department = ? WHERE id = ?";
        
        try {
            $database->execute($sql, array_merge(array_values($data), [$dept_id]));
            logAudit('update', 'departments', $dept_id, $old_data, $data);
            redirectWithMessage($_SERVER['PHP_SELF'], 'تم تحديث القسم بنجاح', 'success');
        } catch (Exception $e) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'حدث خطأ أثناء تحديث القسم', 'danger');
        }
        
    } elseif ($action === 'toggle_status') {
        $dept_id = (int)($_POST['dept_id'] ?? 0);
        $new_status = (int)($_POST['new_status'] ?? 0);
        
        // Get old values for audit
        $old_data = $database->fetchOne("SELECT * FROM departments WHERE id = ?", [$dept_id]);
        
        $sql = "UPDATE departments SET is_active = ? WHERE id = ?";
        
        try {
            $database->execute($sql, [$new_status, $dept_id]);
            logAudit('status_change', 'departments', $dept_id, $old_data, ['is_active' => $new_status]);
            
            $status_text = $new_status ? 'تم تفعيل' : 'تم إلغاء تفعيل';
            redirectWithMessage($_SERVER['PHP_SELF'], $status_text . ' القسم بنجاح', 'success');
        } catch (Exception $e) {
            redirectWithMessage($_SERVER['PHP_SELF'], 'حدث خطأ أثناء تغيير حالة القسم', 'danger');
        }
    }
}

// Get departments list
$departments = $database->fetchAll("SELECT * FROM departments ORDER BY dept_name_ar");

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/dashboard.php">
                            <i class="bi bi-speedometer2"></i>
                            <?php echo $lang['dashboard']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo BASE_URL; ?>/admin/departments.php">
                            <i class="bi bi-building"></i>
                            <?php echo $lang['departments']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/users.php">
                            <i class="bi bi-people"></i>
                            إدارة الموظفين
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/leave-requests.php">
                            <i class="bi bi-calendar-check"></i>
                            طلبات الإجازة
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/reports.php">
                            <i class="bi bi-graph-up"></i>
                            <?php echo $lang['reports']; ?>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-building me-2"></i>
                        إدارة الأقسام
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                            <i class="bi bi-plus-circle me-2"></i>
                            إضافة قسم جديد
                        </button>
                    </div>
                </div>
                
                <!-- Departments Table -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">قائمة الأقسام</h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($departments)): ?>
                            <div class="text-center py-4">
                                <i class="bi bi-building fs-1 text-muted"></i>
                                <p class="text-muted mt-2">لا توجد أقسام مضافة بعد</p>
                                <button type="button" class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addDepartmentModal">
                                    <i class="bi bi-plus-circle me-2"></i>
                                    إضافة أول قسم
                                </button>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>الرمز</th>
                                            <th>اسم القسم</th>
                                            <th>اسم القسم (English)</th>
                                            <th>رئيس القسم</th>
                                            <th>عدد الموظفين</th>
                                            <th>الحالة</th>
                                            <th>تاريخ الإنشاء</th>
                                            <th>الإجراءات</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($departments as $dept): ?>
                                            <?php
                                            // Get employee count for this department
                                            $emp_count = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE department_id = ? AND is_active = 1", [$dept['id']])['count'];
                                            ?>
                                            <tr>
                                                <td>
                                                    <span class="badge bg-secondary"><?php echo preventXSS($dept['dept_code']); ?></span>
                                                </td>
                                                <td>
                                                    <strong><?php echo preventXSS($dept['dept_name_ar']); ?></strong>
                                                    <?php if (!empty($dept['description'])): ?>
                                                        <br><small class="text-muted"><?php echo preventXSS($dept['description']); ?></small>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo preventXSS($dept['dept_name']); ?></td>
                                                <td><?php echo preventXSS($dept['head_of_department'] ?? 'غير محدد'); ?></td>
                                                <td>
                                                    <span class="badge bg-info"><?php echo $emp_count; ?> موظف</span>
                                                </td>
                                                <td>
                                                    <?php if ($dept['is_active']): ?>
                                                        <span class="badge bg-success">نشط</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-danger">غير نشط</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td><?php echo formatDate($dept['created_at']); ?></td>
                                                <td>
                                                    <div class="btn-group" role="group">
                                                        <button type="button" 
                                                                class="btn btn-sm btn-outline-primary" 
                                                                onclick="editDepartment(<?php echo htmlspecialchars(json_encode($dept)); ?>)">
                                                            <i class="bi bi-pencil"></i>
                                                        </button>
                                                        
                                                        <form method="POST" style="display: inline;">
                                                            <?php echo getCSRFTokenField(); ?>
                                                            <input type="hidden" name="action" value="toggle_status">
                                                            <input type="hidden" name="dept_id" value="<?php echo $dept['id']; ?>">
                                                            <input type="hidden" name="new_status" value="<?php echo $dept['is_active'] ? 0 : 1; ?>">
                                                            <button type="submit" 
                                                                    class="btn btn-sm btn-outline-<?php echo $dept['is_active'] ? 'warning' : 'success'; ?>"
                                                                    onclick="return confirm('هل أنت متأكد من تغيير حالة القسم؟')">
                                                                <i class="bi bi-<?php echo $dept['is_active'] ? 'pause' : 'play'; ?>"></i>
                                                            </button>
                                                        </form>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<!-- Add Department Modal -->
<div class="modal fade" id="addDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">إضافة قسم جديد</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <?php echo getCSRFTokenField(); ?>
                <input type="hidden" name="action" value="add">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="dept_code" class="form-label">رمز القسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="dept_code" name="dept_code" required maxlength="20">
                    </div>
                    <div class="mb-3">
                        <label for="dept_name_ar" class="form-label">اسم القسم بالعربية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="dept_name_ar" name="dept_name_ar" required maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="dept_name" class="form-label">اسم القسم بالإنجليزية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="dept_name" name="dept_name" required maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="head_of_department" class="form-label">رئيس القسم</label>
                        <input type="text" class="form-control" id="head_of_department" name="head_of_department" maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">إضافة القسم</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Edit Department Modal -->
<div class="modal fade" id="editDepartmentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">تعديل القسم</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <form method="POST">
                <?php echo getCSRFTokenField(); ?>
                <input type="hidden" name="action" value="edit">
                <input type="hidden" name="dept_id" id="edit_dept_id">
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="edit_dept_code" class="form-label">رمز القسم <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_dept_code" name="dept_code" required maxlength="20">
                    </div>
                    <div class="mb-3">
                        <label for="edit_dept_name_ar" class="form-label">اسم القسم بالعربية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_dept_name_ar" name="dept_name_ar" required maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="edit_dept_name" class="form-label">اسم القسم بالإنجليزية <span class="text-danger">*</span></label>
                        <input type="text" class="form-control" id="edit_dept_name" name="dept_name" required maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="edit_head_of_department" class="form-label">رئيس القسم</label>
                        <input type="text" class="form-control" id="edit_head_of_department" name="head_of_department" maxlength="100">
                    </div>
                    <div class="mb-3">
                        <label for="edit_description" class="form-label">الوصف</label>
                        <textarea class="form-control" id="edit_description" name="description" rows="3"></textarea>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إلغاء</button>
                    <button type="submit" class="btn btn-primary">حفظ التغييرات</button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function editDepartment(dept) {
    document.getElementById('edit_dept_id').value = dept.id;
    document.getElementById('edit_dept_code').value = dept.dept_code;
    document.getElementById('edit_dept_name_ar').value = dept.dept_name_ar;
    document.getElementById('edit_dept_name').value = dept.dept_name;
    document.getElementById('edit_head_of_department').value = dept.head_of_department || '';
    document.getElementById('edit_description').value = dept.description || '';
    
    new bootstrap.Modal(document.getElementById('editDepartmentModal')).show();
}
</script>

<?php include '../includes/footer.php'; ?>
