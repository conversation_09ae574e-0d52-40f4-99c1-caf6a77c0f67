<?php
require_once 'config/config.php';

// Redirect if already logged in
if (isLoggedIn()) {
    header('Location: ' . BASE_URL . '/dashboard.php');
    exit();
}

$error_message = '';
$success_message = '';

// Get departments for dropdown
$departments = getDepartments();

// Handle registration form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFFromPost()) {
        $error_message = 'خطأ في التحقق من الأمان. حاول مرة أخرى.';
    } else {
        $data = [
            'employee_id' => sanitizeInput($_POST['employee_id'] ?? ''),
            'username' => sanitizeInput($_POST['username'] ?? ''),
            'email' => sanitizeInput($_POST['email'] ?? ''),
            'password' => $_POST['password'] ?? '',
            'confirm_password' => $_POST['confirm_password'] ?? '',
            'full_name' => sanitizeInput($_POST['full_name'] ?? ''),
            'full_name_ar' => sanitizeInput($_POST['full_name_ar'] ?? ''),
            'phone' => sanitizeInput($_POST['phone'] ?? ''),
            'address' => sanitizeInput($_POST['address'] ?? ''),
            'address_ar' => sanitizeInput($_POST['address_ar'] ?? ''),
            'department_id' => (int)($_POST['department_id'] ?? 0),
            'hire_date' => sanitizeInput($_POST['hire_date'] ?? '')
        ];
        
        // Validate required fields
        $required_fields = ['employee_id', 'username', 'email', 'password', 'confirm_password', 'full_name', 'full_name_ar', 'department_id'];
        $missing_fields = [];
        
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                $missing_fields[] = $field;
            }
        }
        
        if (!empty($missing_fields)) {
            $error_message = 'جميع الحقول المطلوبة يجب ملؤها';
        } elseif ($data['password'] !== $data['confirm_password']) {
            $error_message = 'كلمات المرور غير متطابقة';
        } else {
            $result = registerUser($data);
            
            if ($result['success']) {
                $success_message = $result['message'];
                // Clear form data on success
                $data = [];
            } else {
                $error_message = $result['message'];
            }
        }
    }
}

$page_title = $lang['register_title'];
include 'includes/header.php';
?>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-7">
            <div class="card shadow-sm mt-4 mb-5">
                <div class="card-header text-center bg-success text-white">
                    <h4 class="mb-0">
                        <i class="bi bi-person-plus me-2"></i>
                        <?php echo $lang['register_title']; ?>
                    </h4>
                </div>
                <div class="card-body p-4">
                    <!-- Organization Info -->
                    <div class="text-center mb-4">
                        <h5 class="text-muted"><?php echo $lang['organization_name']; ?></h5>
                        <p class="small text-muted">إنشاء حساب جديد في نظام إدارة الإجازات</p>
                    </div>
                    
                    <!-- Error/Success Messages -->
                    <?php if (!empty($error_message)): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            <?php echo $error_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <?php if (!empty($success_message)): ?>
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="bi bi-check-circle me-2"></i>
                            <?php echo $success_message; ?>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>
                    
                    <!-- Registration Form -->
                    <form method="POST" action="" class="needs-validation" novalidate>
                        <?php echo getCSRFTokenField(); ?>
                        
                        <div class="row">
                            <!-- Employee ID -->
                            <div class="col-md-6 mb-3">
                                <label for="employee_id" class="form-label">
                                    <i class="bi bi-badge-tm me-1"></i>
                                    <?php echo $lang['employee_id']; ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="employee_id" 
                                       name="employee_id" 
                                       value="<?php echo isset($data['employee_id']) ? preventXSS($data['employee_id']) : ''; ?>"
                                       required 
                                       pattern="[A-Z0-9]{3,20}"
                                       placeholder="مثال: EMP001">
                                <div class="invalid-feedback">
                                    رقم الموظف مطلوب (أحرف كبيرة وأرقام فقط، 3-20 حرف)
                                </div>
                            </div>
                            
                            <!-- Username -->
                            <div class="col-md-6 mb-3">
                                <label for="username" class="form-label">
                                    <i class="bi bi-person me-1"></i>
                                    <?php echo $lang['username']; ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="username" 
                                       name="username" 
                                       value="<?php echo isset($data['username']) ? preventXSS($data['username']) : ''; ?>"
                                       required 
                                       pattern="[a-zA-Z0-9_]{3,20}"
                                       placeholder="اسم المستخدم">
                                <div class="invalid-feedback">
                                    اسم المستخدم مطلوب (أحرف وأرقام فقط، 3-20 حرف)
                                </div>
                            </div>
                        </div>
                        
                        <!-- Email -->
                        <div class="mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope me-1"></i>
                                <?php echo $lang['email']; ?> <span class="text-danger">*</span>
                            </label>
                            <input type="email" 
                                   class="form-control" 
                                   id="email" 
                                   name="email" 
                                   value="<?php echo isset($data['email']) ? preventXSS($data['email']) : ''; ?>"
                                   required 
                                   placeholder="<EMAIL>">
                            <div class="invalid-feedback">
                                البريد الإلكتروني مطلوب وصحيح
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Password -->
                            <div class="col-md-6 mb-3">
                                <label for="password" class="form-label">
                                    <i class="bi bi-lock me-1"></i>
                                    <?php echo $lang['password']; ?> <span class="text-danger">*</span>
                                </label>
                                <input type="password" 
                                       class="form-control" 
                                       id="password" 
                                       name="password" 
                                       required 
                                       minlength="8"
                                       placeholder="كلمة المرور">
                                <div class="invalid-feedback">
                                    كلمة المرور مطلوبة (8 أحرف على الأقل)
                                </div>
                                <div class="form-text">
                                    يجب أن تحتوي على 8 أحرف على الأقل مع أحرف كبيرة وصغيرة وأرقام
                                </div>
                            </div>
                            
                            <!-- Confirm Password -->
                            <div class="col-md-6 mb-3">
                                <label for="confirm_password" class="form-label">
                                    <i class="bi bi-lock-fill me-1"></i>
                                    تأكيد كلمة المرور <span class="text-danger">*</span>
                                </label>
                                <input type="password" 
                                       class="form-control" 
                                       id="confirm_password" 
                                       name="confirm_password" 
                                       required 
                                       placeholder="تأكيد كلمة المرور">
                                <div class="invalid-feedback">
                                    تأكيد كلمة المرور مطلوب
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Full Name -->
                            <div class="col-md-6 mb-3">
                                <label for="full_name" class="form-label">
                                    <i class="bi bi-person-badge me-1"></i>
                                    <?php echo $lang['full_name']; ?> (English) <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="full_name" 
                                       name="full_name" 
                                       value="<?php echo isset($data['full_name']) ? preventXSS($data['full_name']) : ''; ?>"
                                       required 
                                       placeholder="Full Name in English">
                                <div class="invalid-feedback">
                                    الاسم الكامل بالإنجليزية مطلوب
                                </div>
                            </div>
                            
                            <!-- Full Name Arabic -->
                            <div class="col-md-6 mb-3">
                                <label for="full_name_ar" class="form-label">
                                    <i class="bi bi-person-badge-fill me-1"></i>
                                    <?php echo $lang['full_name_ar']; ?> <span class="text-danger">*</span>
                                </label>
                                <input type="text" 
                                       class="form-control" 
                                       id="full_name_ar" 
                                       name="full_name_ar" 
                                       value="<?php echo isset($data['full_name_ar']) ? preventXSS($data['full_name_ar']) : ''; ?>"
                                       required 
                                       placeholder="الاسم الكامل بالعربية">
                                <div class="invalid-feedback">
                                    الاسم الكامل بالعربية مطلوب
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <!-- Department -->
                            <div class="col-md-6 mb-3">
                                <label for="department_id" class="form-label">
                                    <i class="bi bi-building me-1"></i>
                                    <?php echo $lang['department']; ?> <span class="text-danger">*</span>
                                </label>
                                <select class="form-select" id="department_id" name="department_id" required>
                                    <option value="">اختر القسم</option>
                                    <?php foreach ($departments as $dept): ?>
                                        <option value="<?php echo $dept['id']; ?>" 
                                                <?php echo (isset($data['department_id']) && $data['department_id'] == $dept['id']) ? 'selected' : ''; ?>>
                                            <?php echo preventXSS($dept['dept_name_ar']); ?> 
                                            (<?php echo preventXSS($dept['dept_code']); ?>)
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                                <div class="invalid-feedback">
                                    يرجى اختيار القسم
                                </div>
                            </div>
                            
                            <!-- Phone -->
                            <div class="col-md-6 mb-3">
                                <label for="phone" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>
                                    <?php echo $lang['phone']; ?>
                                </label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="phone" 
                                       name="phone" 
                                       value="<?php echo isset($data['phone']) ? preventXSS($data['phone']) : ''; ?>"
                                       placeholder="07xxxxxxxxx">
                            </div>
                        </div>
                        
                        <!-- Hire Date -->
                        <div class="mb-3">
                            <label for="hire_date" class="form-label">
                                <i class="bi bi-calendar me-1"></i>
                                <?php echo $lang['hire_date']; ?>
                            </label>
                            <input type="date" 
                                   class="form-control" 
                                   id="hire_date" 
                                   name="hire_date" 
                                   value="<?php echo isset($data['hire_date']) ? $data['hire_date'] : date('Y-m-d'); ?>">
                        </div>
                        
                        <!-- Address -->
                        <div class="mb-3">
                            <label for="address" class="form-label">
                                <i class="bi bi-geo-alt me-1"></i>
                                <?php echo $lang['address']; ?>
                            </label>
                            <textarea class="form-control" 
                                      id="address" 
                                      name="address" 
                                      rows="2" 
                                      placeholder="العنوان"><?php echo isset($data['address']) ? preventXSS($data['address']) : ''; ?></textarea>
                        </div>
                        
                        <!-- Terms and Conditions -->
                        <div class="mb-3 form-check">
                            <input type="checkbox" class="form-check-input" id="terms" required>
                            <label class="form-check-label" for="terms">
                                أوافق على <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">الشروط والأحكام</a>
                            </label>
                            <div class="invalid-feedback">
                                يجب الموافقة على الشروط والأحكام
                            </div>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-lg">
                                <i class="bi bi-person-plus me-2"></i>
                                <?php echo $lang['register']; ?>
                            </button>
                            <a href="<?php echo BASE_URL; ?>/login.php" class="btn btn-outline-secondary">
                                <i class="bi bi-arrow-left me-2"></i>
                                العودة لتسجيل الدخول
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">الشروط والأحكام</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>شروط استخدام نظام إدارة إجازات الموظفين</h6>
                <p>بإنشاء حساب في هذا النظام، فإنك توافق على:</p>
                <ul>
                    <li>استخدام النظام لأغراض العمل الرسمية فقط</li>
                    <li>عدم مشاركة بيانات الدخول مع أي شخص آخر</li>
                    <li>الالتزام بسياسات الإجازات المعتمدة في المؤسسة</li>
                    <li>تقديم معلومات صحيحة ودقيقة</li>
                    <li>إبلاغ الإدارة عن أي مشاكل أمنية</li>
                </ul>
                <p>تحتفظ الإدارة بالحق في تعديل هذه الشروط أو إلغاء الحسابات المخالفة.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">إغلاق</button>
            </div>
        </div>
    </div>
</div>

<script>
// Password confirmation validation
document.getElementById('confirm_password').addEventListener('input', function() {
    const password = document.getElementById('password').value;
    const confirmPassword = this.value;
    
    if (password !== confirmPassword) {
        this.setCustomValidity('كلمات المرور غير متطابقة');
    } else {
        this.setCustomValidity('');
    }
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include 'includes/footer.php'; ?>
