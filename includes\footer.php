    <!-- Footer -->
    <footer class="bg-light text-center text-muted py-4 mt-5">
        <div class="container">
            <div class="row">
                <div class="col-md-6 text-<?php echo IS_RTL ? 'end' : 'start'; ?>">
                    <p class="mb-0">
                        &copy; <?php echo date('Y'); ?> <?php echo $lang['organization_name']; ?>
                        <br>
                        <small><?php echo $lang['copyright']; ?></small>
                    </p>
                </div>
                <div class="col-md-6 text-<?php echo IS_RTL ? 'start' : 'end'; ?>">
                    <p class="mb-0">
                        <?php echo $lang['app_name']; ?>
                        <br>
                        <small><?php echo $lang['version']; ?> <?php echo APP_VERSION; ?></small>
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Custom JS -->
    <script src="<?php echo ASSETS_URL; ?>/js/main.js"></script>
    
    <script>
        // Global JavaScript variables
        const BASE_URL = '<?php echo BASE_URL; ?>';
        const IS_RTL = <?php echo IS_RTL ? 'true' : 'false'; ?>;
        const CURRENT_LANGUAGE = '<?php echo $current_language; ?>';
        
        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
        
        // Initialize popovers
        var popoverTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="popover"]'));
        var popoverList = popoverTriggerList.map(function (popoverTriggerEl) {
            return new bootstrap.Popover(popoverTriggerEl);
        });
        
        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            $('.alert').fadeOut('slow');
        }, 5000);
        
        // Confirm delete actions
        $(document).on('click', '.btn-delete', function(e) {
            if (!confirm('هل أنت متأكد من الحذف؟')) {
                e.preventDefault();
                return false;
            }
        });
        
        // Form validation
        $(document).ready(function() {
            // Add 'was-validated' class to forms on submit
            $('form').on('submit', function() {
                $(this).addClass('was-validated');
            });
            
            // Real-time validation for required fields
            $('input[required], select[required], textarea[required]').on('blur', function() {
                if ($(this).val() === '') {
                    $(this).addClass('is-invalid');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });
            
            // Email validation
            $('input[type="email"]').on('blur', function() {
                const email = $(this).val();
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                
                if (email && !emailRegex.test(email)) {
                    $(this).addClass('is-invalid');
                } else if (email) {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });
            
            // Date validation
            $('input[type="date"]').on('change', function() {
                const selectedDate = new Date($(this).val());
                const today = new Date();
                today.setHours(0, 0, 0, 0);
                
                if ($(this).hasClass('future-date-only') && selectedDate < today) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('يجب أن يكون التاريخ في المستقبل');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                }
            });
            
            // End date should be after start date
            $('#end_date').on('change', function() {
                const startDate = new Date($('#start_date').val());
                const endDate = new Date($(this).val());
                
                if (startDate && endDate && endDate < startDate) {
                    $(this).addClass('is-invalid');
                    $(this).siblings('.invalid-feedback').text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                } else {
                    $(this).removeClass('is-invalid').addClass('is-valid');
                    calculateLeaveDays();
                }
            });
            
            $('#start_date').on('change', function() {
                const startDate = new Date($(this).val());
                const endDate = new Date($('#end_date').val());
                
                if (startDate && endDate && endDate < startDate) {
                    $('#end_date').addClass('is-invalid');
                    $('#end_date').siblings('.invalid-feedback').text('تاريخ النهاية يجب أن يكون بعد تاريخ البداية');
                } else {
                    $('#end_date').removeClass('is-invalid').addClass('is-valid');
                    calculateLeaveDays();
                }
            });
        });
        
        // Calculate leave days
        function calculateLeaveDays() {
            const startDate = $('#start_date').val();
            const endDate = $('#end_date').val();
            
            if (startDate && endDate) {
                const start = new Date(startDate);
                const end = new Date(endDate);
                
                if (end >= start) {
                    let workingDays = 0;
                    const currentDate = new Date(start);
                    
                    while (currentDate <= end) {
                        const dayOfWeek = currentDate.getDay();
                        if (dayOfWeek !== 5 && dayOfWeek !== 6) { // Not Friday or Saturday
                            workingDays++;
                        }
                        currentDate.setDate(currentDate.getDate() + 1);
                    }
                    
                    $('#total_days').val(workingDays);
                    $('#days_display').text(workingDays + ' يوم');
                }
            }
        }
        
        // Loading spinner
        function showLoading() {
            $('body').append('<div id="loading-overlay" class="position-fixed top-0 start-0 w-100 h-100 d-flex justify-content-center align-items-center" style="background: rgba(0,0,0,0.5); z-index: 9999;"><div class="spinner-border text-light" role="status"><span class="visually-hidden">جاري التحميل...</span></div></div>');
        }
        
        function hideLoading() {
            $('#loading-overlay').remove();
        }
        
        // AJAX form submission
        $('.ajax-form').on('submit', function(e) {
            e.preventDefault();
            
            const form = $(this);
            const formData = new FormData(this);
            
            showLoading();
            
            $.ajax({
                url: form.attr('action'),
                type: form.attr('method') || 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    hideLoading();
                    
                    if (response.success) {
                        if (response.redirect) {
                            window.location.href = response.redirect;
                        } else {
                            location.reload();
                        }
                    } else {
                        alert(response.message || 'حدث خطأ غير متوقع');
                    }
                },
                error: function() {
                    hideLoading();
                    alert('حدث خطأ في الاتصال بالخادم');
                }
            });
        });
    </script>
    
    <?php if (isset($extra_js)): ?>
        <?php echo $extra_js; ?>
    <?php endif; ?>
</body>
</html>
