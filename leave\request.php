<?php
require_once '../config/config.php';
requireLogin();

$user = getCurrentUser();
$page_title = 'طلب إجازة جديد';

// Get user's leave balance
$leave_balance = getUserLeaveBalance($user['id']);

// Handle form submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    if (!validateCSRFFromPost()) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'خطأ في التحقق من الأمان', 'danger');
    }
    
    $data = [
        'start_date' => sanitizeInput($_POST['start_date'] ?? ''),
        'end_date' => sanitizeInput($_POST['end_date'] ?? ''),
        'leave_type' => sanitizeInput($_POST['leave_type'] ?? ''),
        'reason' => sanitizeInput($_POST['reason'] ?? ''),
        'emergency_contact' => sanitizeInput($_POST['emergency_contact'] ?? ''),
        'replacement_employee' => sanitizeInput($_POST['replacement_employee'] ?? '')
    ];
    
    // Validate required fields
    if (empty($data['start_date']) || empty($data['end_date']) || empty($data['leave_type']) || empty($data['reason'])) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'جميع الحقول المطلوبة يجب ملؤها', 'danger');
    }
    
    // Validate dates
    $start_date = new DateTime($data['start_date']);
    $end_date = new DateTime($data['end_date']);
    $today = new DateTime();
    
    if ($start_date < $today) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'تاريخ بداية الإجازة لا يمكن أن يكون في الماضي', 'danger');
    }
    
    if ($end_date < $start_date) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'تاريخ نهاية الإجازة يجب أن يكون بعد تاريخ البداية', 'danger');
    }
    
    // Calculate working days
    $total_days = calculateWorkingDays($data['start_date'], $data['end_date']);
    
    if ($total_days <= 0) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'يجب أن تكون الإجازة يوم عمل واحد على الأقل', 'danger');
    }
    
    // Check if user has enough balance
    if ($total_days > $leave_balance['remaining_days']) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'رصيد الإجازات غير كافي. الرصيد المتاح: ' . $leave_balance['remaining_days'] . ' يوم', 'danger');
    }
    
    // Check for overlapping requests
    $overlap_check = $database->fetchOne("
        SELECT id FROM leave_requests 
        WHERE user_id = ? 
        AND status IN ('pending', 'approved') 
        AND (
            (start_date <= ? AND end_date >= ?) OR
            (start_date <= ? AND end_date >= ?) OR
            (start_date >= ? AND end_date <= ?)
        )
    ", [
        $user['id'],
        $data['start_date'], $data['start_date'],
        $data['end_date'], $data['end_date'],
        $data['start_date'], $data['end_date']
    ]);
    
    if ($overlap_check) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'يوجد طلب إجازة آخر في نفس الفترة', 'danger');
    }
    
    // Generate request number
    $request_number = generateRequestNumber();
    
    // Insert leave request
    $sql = "INSERT INTO leave_requests (
        user_id, request_number, start_date, end_date, total_days, 
        leave_type, reason, emergency_contact, replacement_employee, status
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, 'pending')";
    
    try {
        $request_id = $database->insert($sql, [
            $user['id'],
            $request_number,
            $data['start_date'],
            $data['end_date'],
            $total_days,
            $data['leave_type'],
            $data['reason'],
            $data['emergency_contact'],
            $data['replacement_employee']
        ]);
        
        // Log audit
        logAudit('create', 'leave_requests', $request_id, null, array_merge($data, [
            'total_days' => $total_days,
            'request_number' => $request_number
        ]));
        
        // Send notification to admin (if notification system is implemented)
        // sendLeaveRequestNotification($request_id);
        
        redirectWithMessage(BASE_URL . '/leave/my-requests.php', 'تم تقديم طلب الإجازة بنجاح. رقم الطلب: ' . $request_number, 'success');
        
    } catch (Exception $e) {
        redirectWithMessage($_SERVER['PHP_SELF'], 'حدث خطأ أثناء تقديم الطلب', 'danger');
    }
}

include '../includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/dashboard.php">
                            <i class="bi bi-speedometer2"></i>
                            <?php echo $lang['dashboard']; ?>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo BASE_URL; ?>/leave/request.php">
                            <i class="bi bi-plus-circle"></i>
                            طلب إجازة جديد
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/leave/my-requests.php">
                            <i class="bi bi-list-ul"></i>
                            طلباتي
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/leave/balance.php">
                            <i class="bi bi-wallet2"></i>
                            رصيد الإجازات
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/profile.php">
                            <i class="bi bi-person"></i>
                            <?php echo $lang['profile']; ?>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <i class="bi bi-plus-circle me-2"></i>
                        طلب إجازة جديد
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <a href="<?php echo BASE_URL; ?>/leave/my-requests.php" class="btn btn-outline-secondary">
                            <i class="bi bi-list-ul me-2"></i>
                            عرض طلباتي
                        </a>
                    </div>
                </div>
                
                <!-- Leave Balance Info -->
                <div class="row mb-4">
                    <div class="col-md-4">
                        <div class="card bg-primary text-white">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-wallet2 me-2"></i>
                                    الرصيد الإجمالي
                                </h5>
                                <h3><?php echo $leave_balance['total_balance']; ?> يوم</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-warning text-white">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-calendar-x me-2"></i>
                                    الأيام المستخدمة
                                </h5>
                                <h3><?php echo $leave_balance['used_days']; ?> يوم</h3>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card bg-success text-white">
                            <div class="card-body">
                                <h5 class="card-title">
                                    <i class="bi bi-calendar-check me-2"></i>
                                    الأيام المتبقية
                                </h5>
                                <h3><?php echo $leave_balance['remaining_days']; ?> يوم</h3>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Leave Request Form -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-file-earmark-text me-2"></i>
                            نموذج طلب الإجازة
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if ($leave_balance['remaining_days'] <= 0): ?>
                            <div class="alert alert-warning" role="alert">
                                <i class="bi bi-exclamation-triangle me-2"></i>
                                <strong>تنبيه:</strong> لا يوجد لديك رصيد إجازات متاح حالياً.
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST" class="needs-validation" novalidate>
                            <?php echo getCSRFTokenField(); ?>
                            
                            <div class="row">
                                <!-- Leave Type -->
                                <div class="col-md-6 mb-3">
                                    <label for="leave_type" class="form-label">
                                        <i class="bi bi-tag me-1"></i>
                                        نوع الإجازة <span class="text-danger">*</span>
                                    </label>
                                    <select class="form-select" id="leave_type" name="leave_type" required>
                                        <option value="">اختر نوع الإجازة</option>
                                        <option value="annual">إجازة سنوية</option>
                                        <option value="sick">إجازة مرضية</option>
                                        <option value="emergency">إجازة طارئة</option>
                                        <option value="personal">إجازة شخصية</option>
                                        <option value="maternity">إجازة أمومة</option>
                                        <option value="paternity">إجازة أبوة</option>
                                    </select>
                                    <div class="invalid-feedback">
                                        يرجى اختيار نوع الإجازة
                                    </div>
                                </div>
                                
                                <!-- Replacement Employee -->
                                <div class="col-md-6 mb-3">
                                    <label for="replacement_employee" class="form-label">
                                        <i class="bi bi-person-check me-1"></i>
                                        الموظف البديل
                                    </label>
                                    <input type="text" 
                                           class="form-control" 
                                           id="replacement_employee" 
                                           name="replacement_employee" 
                                           placeholder="اسم الموظف البديل">
                                </div>
                            </div>
                            
                            <div class="row">
                                <!-- Start Date -->
                                <div class="col-md-6 mb-3">
                                    <label for="start_date" class="form-label">
                                        <i class="bi bi-calendar me-1"></i>
                                        تاريخ بداية الإجازة <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="start_date" 
                                           name="start_date" 
                                           required 
                                           min="<?php echo date('Y-m-d'); ?>">
                                    <div class="invalid-feedback">
                                        تاريخ بداية الإجازة مطلوب
                                    </div>
                                </div>
                                
                                <!-- End Date -->
                                <div class="col-md-6 mb-3">
                                    <label for="end_date" class="form-label">
                                        <i class="bi bi-calendar-check me-1"></i>
                                        تاريخ نهاية الإجازة <span class="text-danger">*</span>
                                    </label>
                                    <input type="date" 
                                           class="form-control" 
                                           id="end_date" 
                                           name="end_date" 
                                           required 
                                           min="<?php echo date('Y-m-d'); ?>">
                                    <div class="invalid-feedback">
                                        تاريخ نهاية الإجازة مطلوب
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Days Calculation Display -->
                            <div class="row mb-3">
                                <div class="col-12">
                                    <div class="alert alert-info" id="days-calculation" style="display: none;">
                                        <i class="bi bi-calculator me-2"></i>
                                        <span id="days-text"></span>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Emergency Contact -->
                            <div class="mb-3">
                                <label for="emergency_contact" class="form-label">
                                    <i class="bi bi-telephone me-1"></i>
                                    رقم الاتصال في حالة الطوارئ
                                </label>
                                <input type="tel" 
                                       class="form-control" 
                                       id="emergency_contact" 
                                       name="emergency_contact" 
                                       placeholder="07xxxxxxxxx">
                            </div>
                            
                            <!-- Reason -->
                            <div class="mb-3">
                                <label for="reason" class="form-label">
                                    <i class="bi bi-chat-text me-1"></i>
                                    سبب الإجازة <span class="text-danger">*</span>
                                </label>
                                <textarea class="form-control" 
                                          id="reason" 
                                          name="reason" 
                                          rows="4" 
                                          required 
                                          placeholder="اكتب سبب طلب الإجازة..."></textarea>
                                <div class="invalid-feedback">
                                    سبب الإجازة مطلوب
                                </div>
                            </div>
                            
                            <!-- Terms and Conditions -->
                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="terms" required>
                                <label class="form-check-label" for="terms">
                                    أتعهد بصحة المعلومات المقدمة وأوافق على شروط وأحكام الإجازات
                                </label>
                                <div class="invalid-feedback">
                                    يجب الموافقة على الشروط والأحكام
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                                <a href="<?php echo BASE_URL; ?>/dashboard.php" class="btn btn-secondary me-md-2">
                                    <i class="bi bi-arrow-left me-2"></i>
                                    إلغاء
                                </a>
                                <button type="submit" 
                                        class="btn btn-primary" 
                                        <?php echo ($leave_balance['remaining_days'] <= 0) ? 'disabled' : ''; ?>>
                                    <i class="bi bi-send me-2"></i>
                                    تقديم الطلب
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <!-- Leave Policy Info -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            سياسة الإجازات
                        </h6>
                    </div>
                    <div class="card-body">
                        <ul class="list-unstyled mb-0">
                            <li><i class="bi bi-check-circle text-success me-2"></i>يحق لكل موظف 3 أيام إجازة شهرياً</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>يمكن ترحيل الإجازات غير المستخدمة للشهر التالي</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>يجب تقديم طلب الإجازة قبل 48 ساعة على الأقل</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>أيام العمل من السبت إلى الخميس (الجمعة والسبت عطلة)</li>
                            <li><i class="bi bi-check-circle text-success me-2"></i>يتم احتساب أيام العمل فقط في الإجازة</li>
                        </ul>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<script>
// Calculate working days when dates change
function calculateDays() {
    const startDate = document.getElementById('start_date').value;
    const endDate = document.getElementById('end_date').value;
    
    if (startDate && endDate) {
        const start = new Date(startDate);
        const end = new Date(endDate);
        
        if (end >= start) {
            // Calculate working days (excluding Friday and Saturday)
            let workingDays = 0;
            let currentDate = new Date(start);
            
            while (currentDate <= end) {
                const dayOfWeek = currentDate.getDay();
                // 0 = Sunday, 1 = Monday, ..., 6 = Saturday
                // Working days: Sunday(0) to Thursday(4)
                if (dayOfWeek >= 0 && dayOfWeek <= 4) {
                    workingDays++;
                }
                currentDate.setDate(currentDate.getDate() + 1);
            }
            
            const daysCalculation = document.getElementById('days-calculation');
            const daysText = document.getElementById('days-text');
            
            if (workingDays > 0) {
                daysText.textContent = `إجمالي أيام العمل: ${workingDays} يوم`;
                daysCalculation.style.display = 'block';
                
                // Check if exceeds available balance
                const availableDays = <?php echo $leave_balance['remaining_days']; ?>;
                if (workingDays > availableDays) {
                    daysCalculation.className = 'alert alert-danger';
                    daysText.textContent += ` (يتجاوز الرصيد المتاح: ${availableDays} يوم)`;
                } else {
                    daysCalculation.className = 'alert alert-info';
                }
            } else {
                daysText.textContent = 'لا توجد أيام عمل في الفترة المحددة';
                daysCalculation.className = 'alert alert-warning';
                daysCalculation.style.display = 'block';
            }
        } else {
            document.getElementById('days-calculation').style.display = 'none';
        }
    } else {
        document.getElementById('days-calculation').style.display = 'none';
    }
}

// Add event listeners
document.getElementById('start_date').addEventListener('change', calculateDays);
document.getElementById('end_date').addEventListener('change', calculateDays);

// Set minimum end date when start date changes
document.getElementById('start_date').addEventListener('change', function() {
    document.getElementById('end_date').min = this.value;
});

// Form validation
(function() {
    'use strict';
    window.addEventListener('load', function() {
        var forms = document.getElementsByClassName('needs-validation');
        var validation = Array.prototype.filter.call(forms, function(form) {
            form.addEventListener('submit', function(event) {
                if (form.checkValidity() === false) {
                    event.preventDefault();
                    event.stopPropagation();
                }
                form.classList.add('was-validated');
            }, false);
        });
    }, false);
})();
</script>

<?php include '../includes/footer.php'; ?>
