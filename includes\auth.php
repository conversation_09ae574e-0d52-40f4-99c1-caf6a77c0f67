<?php
/**
 * Authentication Functions
 * Employee Leave Management System
 */

/**
 * Authenticate user login
 */
function authenticateUser($username, $password) {
    global $database;
    
    // Check rate limiting
    if (!checkLoginAttempts($username)) {
        return [
            'success' => false,
            'message' => 'تم تجاوز عدد محاولات تسجيل الدخول المسموح. حاول مرة أخرى بعد 15 دقيقة.'
        ];
    }
    
    // Get user from database
    $sql = "SELECT u.*, d.dept_name, d.dept_name_ar, r.role_name, r.role_name_ar 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            LEFT JOIN roles r ON u.role_id = r.id 
            WHERE (u.username = ? OR u.email = ?) AND u.is_active = 1";
    
    $user = $database->fetchOne($sql, [$username, $username]);
    
    if (!$user) {
        recordFailedLogin($username);
        return [
            'success' => false,
            'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
        ];
    }
    
    // Verify password
    if (!verifyPassword($password, $user['password_hash'])) {
        recordFailedLogin($username);
        logSecurityEvent('failed_login', ['username' => $username]);
        return [
            'success' => false,
            'message' => 'اسم المستخدم أو كلمة المرور غير صحيحة'
        ];
    }
    
    // Reset login attempts on successful login
    resetLoginAttempts($username);
    
    // Update last login
    $database->execute("UPDATE users SET last_login = NOW() WHERE id = ?", [$user['id']]);
    
    // Set session variables
    $_SESSION['user_id'] = $user['id'];
    $_SESSION['username'] = $user['username'];
    $_SESSION['full_name'] = $user['full_name'];
    $_SESSION['role_name'] = $user['role_name'];
    $_SESSION['department_id'] = $user['department_id'];
    $_SESSION['last_activity'] = time();
    
    // Log successful login
    logAudit('login', 'users', $user['id']);
    logSecurityEvent('successful_login', ['username' => $username]);
    
    return [
        'success' => true,
        'message' => 'تم تسجيل الدخول بنجاح',
        'user' => $user
    ];
}

/**
 * Register new user
 */
function registerUser($data) {
    global $database;
    
    // Validate required fields
    $required_fields = ['employee_id', 'username', 'email', 'password', 'full_name', 'full_name_ar', 'department_id'];
    foreach ($required_fields as $field) {
        if (empty($data[$field])) {
            return [
                'success' => false,
                'message' => 'جميع الحقول مطلوبة'
            ];
        }
    }
    
    // Validate input formats
    if (!validateInput($data['employee_id'], 'employee_id')) {
        return [
            'success' => false,
            'message' => 'رقم الموظف غير صحيح'
        ];
    }
    
    if (!validateInput($data['username'], 'username')) {
        return [
            'success' => false,
            'message' => 'اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط (3-20 حرف)'
        ];
    }
    
    if (!isValidEmail($data['email'])) {
        return [
            'success' => false,
            'message' => 'البريد الإلكتروني غير صحيح'
        ];
    }
    
    // Validate password strength
    $password_errors = validatePasswordStrength($data['password']);
    if (!empty($password_errors)) {
        return [
            'success' => false,
            'message' => implode('<br>', $password_errors)
        ];
    }
    
    // Check for existing user
    $sql = "SELECT id FROM users WHERE employee_id = ? OR username = ? OR email = ?";
    $existing = $database->fetchOne($sql, [$data['employee_id'], $data['username'], $data['email']]);
    
    if ($existing) {
        return [
            'success' => false,
            'message' => 'رقم الموظف أو اسم المستخدم أو البريد الإلكتروني مستخدم بالفعل'
        ];
    }
    
    // Check if department exists
    $dept = $database->fetchOne("SELECT id FROM departments WHERE id = ? AND is_active = 1", [$data['department_id']]);
    if (!$dept) {
        return [
            'success' => false,
            'message' => 'القسم المحدد غير موجود'
        ];
    }
    
    try {
        $database->beginTransaction();
        
        // Hash password
        $password_hash = hashPassword($data['password']);
        
        // Insert user
        $sql = "INSERT INTO users (employee_id, username, email, password_hash, full_name, full_name_ar, 
                phone, address, address_ar, department_id, hire_date) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";
        
        $params = [
            $data['employee_id'],
            $data['username'],
            $data['email'],
            $password_hash,
            $data['full_name'],
            $data['full_name_ar'],
            $data['phone'] ?? null,
            $data['address'] ?? null,
            $data['address_ar'] ?? null,
            $data['department_id'],
            $data['hire_date'] ?? date('Y-m-d')
        ];
        
        $user_id = $database->insert($sql, $params);
        
        // Create initial leave balance for current month
        $year = date('Y');
        $month = date('m');
        $monthly_allocation = MONTHLY_LEAVE_ALLOCATION;
        
        $sql = "INSERT INTO leave_balances (user_id, year, month, monthly_allocation, remaining_days, total_balance) 
                VALUES (?, ?, ?, ?, ?, ?)";
        
        $database->execute($sql, [$user_id, $year, $month, $monthly_allocation, $monthly_allocation, $monthly_allocation]);
        
        $database->commit();
        
        // Log registration
        logAudit('register', 'users', $user_id, null, $data);
        logSecurityEvent('user_registration', ['username' => $data['username'], 'employee_id' => $data['employee_id']]);
        
        return [
            'success' => true,
            'message' => 'تم إنشاء الحساب بنجاح',
            'user_id' => $user_id
        ];
        
    } catch (Exception $e) {
        $database->rollback();
        error_log("Registration error: " . $e->getMessage());
        
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء إنشاء الحساب. حاول مرة أخرى.'
        ];
    }
}

/**
 * Logout user
 */
function logoutUser() {
    if (isset($_SESSION['user_id'])) {
        logAudit('logout', 'users', $_SESSION['user_id']);
        logSecurityEvent('user_logout', ['username' => $_SESSION['username'] ?? 'unknown']);
    }
    
    // Destroy session
    session_destroy();
    
    // Clear session cookie
    if (ini_get("session.use_cookies")) {
        $params = session_get_cookie_params();
        setcookie(session_name(), '', time() - 42000,
            $params["path"], $params["domain"],
            $params["secure"], $params["httponly"]
        );
    }
    
    // Start new session
    session_start();
    
    return true;
}

/**
 * Change user password
 */
function changePassword($user_id, $current_password, $new_password) {
    global $database;
    
    // Get current user
    $user = $database->fetchOne("SELECT password_hash FROM users WHERE id = ?", [$user_id]);
    
    if (!$user) {
        return [
            'success' => false,
            'message' => 'المستخدم غير موجود'
        ];
    }
    
    // Verify current password
    if (!verifyPassword($current_password, $user['password_hash'])) {
        return [
            'success' => false,
            'message' => 'كلمة المرور الحالية غير صحيحة'
        ];
    }
    
    // Validate new password strength
    $password_errors = validatePasswordStrength($new_password);
    if (!empty($password_errors)) {
        return [
            'success' => false,
            'message' => implode('<br>', $password_errors)
        ];
    }
    
    // Hash new password
    $new_password_hash = hashPassword($new_password);
    
    // Update password
    $sql = "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?";
    $result = $database->execute($sql, [$new_password_hash, $user_id]);
    
    if ($result) {
        logAudit('password_change', 'users', $user_id);
        logSecurityEvent('password_change', ['user_id' => $user_id]);
        
        return [
            'success' => true,
            'message' => 'تم تغيير كلمة المرور بنجاح'
        ];
    } else {
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء تغيير كلمة المرور'
        ];
    }
}

/**
 * Reset password (admin function)
 */
function resetUserPassword($user_id, $new_password = null) {
    global $database;
    
    // Generate random password if not provided
    if (!$new_password) {
        $new_password = generateSecurePassword();
    }
    
    // Validate password strength
    $password_errors = validatePasswordStrength($new_password);
    if (!empty($password_errors)) {
        return [
            'success' => false,
            'message' => implode('<br>', $password_errors)
        ];
    }
    
    // Hash password
    $password_hash = hashPassword($new_password);
    
    // Update password
    $sql = "UPDATE users SET password_hash = ?, updated_at = NOW() WHERE id = ?";
    $result = $database->execute($sql, [$password_hash, $user_id]);
    
    if ($result) {
        logAudit('password_reset', 'users', $user_id);
        logSecurityEvent('admin_password_reset', ['target_user_id' => $user_id]);
        
        return [
            'success' => true,
            'message' => 'تم إعادة تعيين كلمة المرور بنجاح',
            'new_password' => $new_password
        ];
    } else {
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء إعادة تعيين كلمة المرور'
        ];
    }
}

/**
 * Update user profile
 */
function updateUserProfile($user_id, $data) {
    global $database;
    
    // Validate email if provided
    if (!empty($data['email']) && !isValidEmail($data['email'])) {
        return [
            'success' => false,
            'message' => 'البريد الإلكتروني غير صحيح'
        ];
    }
    
    // Check for duplicate email
    if (!empty($data['email'])) {
        $existing = $database->fetchOne("SELECT id FROM users WHERE email = ? AND id != ?", [$data['email'], $user_id]);
        if ($existing) {
            return [
                'success' => false,
                'message' => 'البريد الإلكتروني مستخدم بالفعل'
            ];
        }
    }
    
    // Build update query
    $fields = [];
    $params = [];
    
    $allowed_fields = ['email', 'full_name', 'full_name_ar', 'phone', 'address', 'address_ar'];
    
    foreach ($allowed_fields as $field) {
        if (isset($data[$field])) {
            $fields[] = "$field = ?";
            $params[] = $data[$field];
        }
    }
    
    if (empty($fields)) {
        return [
            'success' => false,
            'message' => 'لا توجد بيانات للتحديث'
        ];
    }
    
    $params[] = $user_id;
    
    $sql = "UPDATE users SET " . implode(', ', $fields) . ", updated_at = NOW() WHERE id = ?";
    $result = $database->execute($sql, $params);
    
    if ($result) {
        logAudit('profile_update', 'users', $user_id, null, $data);
        
        return [
            'success' => true,
            'message' => 'تم تحديث الملف الشخصي بنجاح'
        ];
    } else {
        return [
            'success' => false,
            'message' => 'حدث خطأ أثناء تحديث الملف الشخصي'
        ];
    }
}
?>
