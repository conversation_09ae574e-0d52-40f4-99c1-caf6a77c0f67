<?php
/**
 * Main Configuration File
 * Employee Leave Management System
 * Southern Technical University Presidency
 */

// Start session with secure settings
if (session_status() == PHP_SESSION_NONE) {
    ini_set('session.cookie_httponly', 1);
    ini_set('session.cookie_secure', 0); // Set to 1 for HTTPS
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_samesite', 'Strict');
    session_start();
}

// Error reporting (disable in production)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('Asia/Baghdad');

// Application settings
define('APP_NAME', 'Employee Leave Management System');
define('APP_NAME_AR', 'نظام إدارة إجازات الموظفين');
define('APP_VERSION', '1.0.0');
define('ORGANIZATION_NAME', 'Southern Technical University Presidency');
define('ORGANIZATION_NAME_AR', 'رئاسة الجامعة التقنية الجنوبية');

// Directory paths
define('ROOT_PATH', dirname(__DIR__));
define('CONFIG_PATH', ROOT_PATH . '/config');
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// URL paths
define('BASE_URL', 'http://localhost/NEW');
define('ASSETS_URL', BASE_URL . '/assets');
define('UPLOADS_URL', BASE_URL . '/uploads');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_TIMEOUT', 3600); // 1 hour
define('MAX_LOGIN_ATTEMPTS', 5);
define('LOGIN_LOCKOUT_TIME', 900); // 15 minutes

// Leave system settings
define('MONTHLY_LEAVE_ALLOCATION', 3);
define('MAX_LEAVE_DAYS_PER_REQUEST', 30);
define('WORKING_DAYS_PER_WEEK', 5);

// File upload settings
define('MAX_FILE_SIZE', 5 * 1024 * 1024); // 5MB
define('ALLOWED_FILE_TYPES', ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png']);

// Email settings (configure for your SMTP server)
define('SMTP_HOST', 'smtp.gmail.com');
define('SMTP_PORT', 587);
define('SMTP_USERNAME', '');
define('SMTP_PASSWORD', '');
define('SMTP_FROM_EMAIL', '<EMAIL>');
define('SMTP_FROM_NAME', 'Leave Management System');

// Language settings
define('DEFAULT_LANGUAGE', 'ar');
define('SUPPORTED_LANGUAGES', ['ar', 'en']);

// Include required files
require_once CONFIG_PATH . '/database.php';
require_once INCLUDES_PATH . '/functions.php';
require_once INCLUDES_PATH . '/auth.php';
require_once INCLUDES_PATH . '/security.php';

// Initialize language
$current_language = isset($_SESSION['language']) ? $_SESSION['language'] : DEFAULT_LANGUAGE;
if (isset($_GET['lang']) && in_array($_GET['lang'], SUPPORTED_LANGUAGES)) {
    $current_language = $_GET['lang'];
    $_SESSION['language'] = $current_language;
}

// Load language file
$lang = [];
$lang_file = INCLUDES_PATH . '/languages/' . $current_language . '.php';
if (file_exists($lang_file)) {
    require_once $lang_file;
}

// Set RTL direction for Arabic
$is_rtl = ($current_language === 'ar');
define('IS_RTL', $is_rtl);

// Auto-logout on session timeout
if (isset($_SESSION['user_id']) && isset($_SESSION['last_activity'])) {
    if (time() - $_SESSION['last_activity'] > SESSION_TIMEOUT) {
        session_destroy();
        header('Location: ' . BASE_URL . '/login.php?timeout=1');
        exit();
    }
    $_SESSION['last_activity'] = time();
}

// CSRF token generation
if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
    $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
}

/**
 * Get current user information
 */
function getCurrentUser() {
    global $database;
    
    if (!isset($_SESSION['user_id'])) {
        return null;
    }
    
    $sql = "SELECT u.*, d.dept_name, d.dept_name_ar, r.role_name, r.role_name_ar 
            FROM users u 
            LEFT JOIN departments d ON u.department_id = d.id 
            LEFT JOIN roles r ON u.role_id = r.id 
            WHERE u.id = ? AND u.is_active = 1";
    
    return $database->fetchOne($sql, [$_SESSION['user_id']]);
}

/**
 * Check if user is logged in
 */
function isLoggedIn() {
    return isset($_SESSION['user_id']) && !empty($_SESSION['user_id']);
}

/**
 * Check if user has specific role
 */
function hasRole($role) {
    $user = getCurrentUser();
    return $user && $user['role_name'] === $role;
}

/**
 * Redirect if not logged in
 */
function requireLogin() {
    if (!isLoggedIn()) {
        header('Location: ' . BASE_URL . '/login.php');
        exit();
    }
}

/**
 * Redirect if not admin
 */
function requireAdmin() {
    requireLogin();
    if (!hasRole('admin')) {
        header('Location: ' . BASE_URL . '/dashboard.php');
        exit();
    }
}

/**
 * Get system setting
 */
function getSetting($key, $default = null) {
    global $database;
    
    $sql = "SELECT setting_value FROM system_settings WHERE setting_key = ?";
    $result = $database->fetchOne($sql, [$key]);
    
    return $result ? $result['setting_value'] : $default;
}

/**
 * Update system setting
 */
function updateSetting($key, $value) {
    global $database;
    
    $sql = "INSERT INTO system_settings (setting_key, setting_value) 
            VALUES (?, ?) 
            ON DUPLICATE KEY UPDATE setting_value = VALUES(setting_value)";
    
    return $database->execute($sql, [$key, $value]);
}
?>
