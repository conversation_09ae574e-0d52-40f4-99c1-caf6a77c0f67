<!DOCTYPE html>
<html lang="<?php echo $current_language; ?>" dir="<?php echo IS_RTL ? 'rtl' : 'ltr'; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($page_title) ? $page_title . ' - ' : ''; ?><?php echo $lang['app_name']; ?></title>
    
    <!-- Bootstrap CSS -->
    <?php if (IS_RTL): ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <?php else: ?>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <?php endif; ?>
    
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css">
    
    <!-- Google Fonts for Arabic -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link rel="stylesheet" href="<?php echo ASSETS_URL; ?>/css/style.css">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="<?php echo ASSETS_URL; ?>/images/favicon.ico">
    
    <style>
        body {
            font-family: 'Noto Sans Arabic', sans-serif;
            background-color: #f8f9fa;
        }
        
        .navbar-brand {
            font-weight: 600;
            font-size: 1.2rem;
        }
        
        .card {
            border: none;
            box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
            border-radius: 0.5rem;
        }
        
        .card-header {
            background-color: #fff;
            border-bottom: 1px solid #dee2e6;
            font-weight: 500;
        }
        
        .btn {
            border-radius: 0.375rem;
            font-weight: 500;
        }
        
        .table th {
            font-weight: 600;
            background-color: #f8f9fa;
            border-top: none;
        }
        
        .sidebar {
            background-color: #343a40;
            min-height: calc(100vh - 56px);
        }
        
        .sidebar .nav-link {
            color: #adb5bd;
            padding: 0.75rem 1rem;
            border-radius: 0.375rem;
            margin: 0.125rem 0.5rem;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            color: #fff;
            background-color: #495057;
        }
        
        .sidebar .nav-link i {
            margin-<?php echo IS_RTL ? 'left' : 'right'; ?>: 0.5rem;
        }
        
        .main-content {
            padding: 2rem;
        }
        
        .stats-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 1rem;
        }
        
        .stats-card .card-body {
            padding: 1.5rem;
        }
        
        .stats-card h3 {
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 0.5rem;
        }
        
        .badge {
            font-size: 0.75rem;
            font-weight: 500;
        }
        
        .alert {
            border: none;
            border-radius: 0.5rem;
        }
        
        .form-control,
        .form-select {
            border-radius: 0.375rem;
            border: 1px solid #ced4da;
        }
        
        .form-control:focus,
        .form-select:focus {
            border-color: #86b7fe;
            box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
        }
        
        .language-switcher {
            margin-<?php echo IS_RTL ? 'right' : 'left'; ?>: 1rem;
        }
        
        .language-switcher a {
            color: #6c757d;
            text-decoration: none;
            padding: 0.25rem 0.5rem;
            border-radius: 0.25rem;
        }
        
        .language-switcher a:hover {
            background-color: #e9ecef;
        }
        
        .language-switcher a.active {
            background-color: #0d6efd;
            color: white;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .main-content {
                padding: 1rem;
            }
        }
    </style>
    
    <?php if (isset($extra_css)): ?>
        <?php echo $extra_css; ?>
    <?php endif; ?>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="<?php echo BASE_URL; ?>">
                <i class="bi bi-calendar-check me-2"></i>
                <?php echo $lang['app_name']; ?>
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item">
                            <a class="nav-link <?php echo isActivePage('dashboard'); ?>" href="<?php echo BASE_URL; ?>/dashboard.php">
                                <i class="bi bi-speedometer2"></i> <?php echo $lang['dashboard']; ?>
                            </a>
                        </li>
                        
                        <?php if (hasRole('admin')): ?>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                    <i class="bi bi-gear"></i> الإدارة
                                </a>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/departments.php">
                                        <i class="bi bi-building"></i> <?php echo $lang['departments']; ?>
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/users.php">
                                        <i class="bi bi-people"></i> الموظفين
                                    </a></li>
                                    <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/admin/reports.php">
                                        <i class="bi bi-graph-up"></i> <?php echo $lang['reports']; ?>
                                    </a></li>
                                </ul>
                            </li>
                        <?php endif; ?>
                        
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-calendar-event"></i> الإجازات
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/leave/request.php">
                                    <i class="bi bi-plus-circle"></i> طلب إجازة جديد
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/leave/my-requests.php">
                                    <i class="bi bi-list-ul"></i> طلباتي
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/leave/balance.php">
                                    <i class="bi bi-wallet2"></i> رصيد الإجازات
                                </a></li>
                            </ul>
                        </li>
                    <?php endif; ?>
                </ul>
                
                <ul class="navbar-nav">
                    <!-- Language Switcher -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-translate"></i> <?php echo $current_language === 'ar' ? 'العربية' : 'English'; ?>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item <?php echo $current_language === 'ar' ? 'active' : ''; ?>" 
                                   href="?lang=ar">العربية</a></li>
                            <li><a class="dropdown-item <?php echo $current_language === 'en' ? 'active' : ''; ?>" 
                                   href="?lang=en">English</a></li>
                        </ul>
                    </li>
                    
                    <?php if (isLoggedIn()): ?>
                        <li class="nav-item dropdown">
                            <a class="nav-link dropdown-toggle" href="#" role="button" data-bs-toggle="dropdown">
                                <i class="bi bi-person-circle"></i> 
                                <?php 
                                $user = getCurrentUser();
                                echo $user ? $user['full_name'] : $lang['welcome'];
                                ?>
                            </a>
                            <ul class="dropdown-menu">
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/profile.php">
                                    <i class="bi bi-person"></i> <?php echo $lang['profile']; ?>
                                </a></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/settings.php">
                                    <i class="bi bi-gear"></i> <?php echo $lang['settings']; ?>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="<?php echo BASE_URL; ?>/logout.php">
                                    <i class="bi bi-box-arrow-right"></i> <?php echo $lang['logout']; ?>
                                </a></li>
                            </ul>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/login.php">
                                <i class="bi bi-box-arrow-in-right"></i> <?php echo $lang['login']; ?>
                            </a>
                        </li>
                    <?php endif; ?>
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Flash Messages -->
    <div class="container-fluid mt-3">
        <?php displayFlashMessage(); ?>
    </div>
