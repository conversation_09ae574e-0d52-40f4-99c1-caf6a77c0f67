<?php
require_once 'config/config.php';
requireLogin();

$user = getCurrentUser();
$page_title = $lang['dashboard'];

// Get dashboard statistics
$stats = [];

if (hasRole('admin')) {
    // Admin dashboard statistics
    $stats['total_employees'] = $database->fetchOne("SELECT COUNT(*) as count FROM users WHERE is_active = 1")['count'];
    $stats['total_departments'] = $database->fetchOne("SELECT COUNT(*) as count FROM departments WHERE is_active = 1")['count'];
    $stats['pending_requests'] = $database->fetchOne("SELECT COUNT(*) as count FROM leave_requests WHERE status = 'pending'")['count'];
    $stats['approved_requests'] = $database->fetchOne("SELECT COUNT(*) as count FROM leave_requests WHERE status = 'approved' AND MONTH(created_at) = MONTH(CURRENT_DATE())")['count'];
    
    // Recent leave requests
    $recent_requests = $database->fetchAll("
        SELECT lr.*, u.full_name_ar, u.employee_id, d.dept_name_ar 
        FROM leave_requests lr 
        JOIN users u ON lr.user_id = u.id 
        JOIN departments d ON u.department_id = d.id 
        ORDER BY lr.created_at DESC 
        LIMIT 10
    ");
    
} else {
    // Employee dashboard statistics
    $user_balance = getUserLeaveBalance($user['id']);
    $stats['leave_balance'] = $user_balance['total_balance'];
    $stats['used_days'] = $user_balance['used_days'];
    $stats['remaining_days'] = $user_balance['remaining_days'];
    $stats['my_requests'] = $database->fetchOne("SELECT COUNT(*) as count FROM leave_requests WHERE user_id = ?", [$user['id']])['count'];
    
    // My recent requests
    $my_requests = $database->fetchAll("
        SELECT * FROM leave_requests 
        WHERE user_id = ? 
        ORDER BY created_at DESC 
        LIMIT 5
    ", [$user['id']]);
}

include 'includes/header.php';
?>

<div class="container-fluid">
    <div class="row">
        <!-- Sidebar -->
        <nav class="col-md-3 col-lg-2 d-md-block sidebar collapse">
            <div class="position-sticky pt-3">
                <ul class="nav flex-column">
                    <li class="nav-item">
                        <a class="nav-link active" href="<?php echo BASE_URL; ?>/dashboard.php">
                            <i class="bi bi-speedometer2"></i>
                            <?php echo $lang['dashboard']; ?>
                        </a>
                    </li>
                    
                    <?php if (hasRole('admin')): ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/departments.php">
                                <i class="bi bi-building"></i>
                                <?php echo $lang['departments']; ?>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/users.php">
                                <i class="bi bi-people"></i>
                                إدارة الموظفين
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/leave-requests.php">
                                <i class="bi bi-calendar-check"></i>
                                طلبات الإجازة
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/admin/reports.php">
                                <i class="bi bi-graph-up"></i>
                                <?php echo $lang['reports']; ?>
                            </a>
                        </li>
                    <?php else: ?>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/leave/request.php">
                                <i class="bi bi-plus-circle"></i>
                                طلب إجازة جديد
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/leave/my-requests.php">
                                <i class="bi bi-list-ul"></i>
                                طلباتي
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo BASE_URL; ?>/leave/balance.php">
                                <i class="bi bi-wallet2"></i>
                                رصيد الإجازات
                            </a>
                        </li>
                    <?php endif; ?>
                    
                    <li class="nav-item">
                        <a class="nav-link" href="<?php echo BASE_URL; ?>/profile.php">
                            <i class="bi bi-person"></i>
                            <?php echo $lang['profile']; ?>
                        </a>
                    </li>
                </ul>
            </div>
        </nav>
        
        <!-- Main content -->
        <main class="col-md-9 ms-sm-auto col-lg-10 px-md-4">
            <div class="main-content">
                <!-- Header -->
                <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
                    <h1 class="h2">
                        <?php echo hasRole('admin') ? $lang['admin_dashboard'] : $lang['employee_dashboard']; ?>
                    </h1>
                    <div class="btn-toolbar mb-2 mb-md-0">
                        <div class="btn-group me-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" onclick="location.reload()">
                                <i class="bi bi-arrow-clockwise"></i> تحديث
                            </button>
                        </div>
                    </div>
                </div>
                
                <!-- Welcome Message -->
                <div class="alert alert-info" role="alert">
                    <h4 class="alert-heading">
                        <i class="bi bi-person-circle me-2"></i>
                        مرحباً، <?php echo preventXSS($user['full_name_ar']); ?>!
                    </h4>
                    <p class="mb-0">
                        <?php if (hasRole('admin')): ?>
                            مرحباً بك في لوحة تحكم المدير. يمكنك من هنا إدارة الموظفين والأقسام ومراجعة طلبات الإجازة.
                        <?php else: ?>
                            مرحباً بك في نظام إدارة الإجازات. يمكنك تقديم طلبات الإجازة ومتابعة رصيدك من هنا.
                        <?php endif; ?>
                    </p>
                </div>
                
                <!-- Statistics Cards -->
                <div class="row mb-4">
                    <?php if (hasRole('admin')): ?>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['total_employees']; ?></h3>
                                            <p class="mb-0">إجمالي الموظفين</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-people fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['total_departments']; ?></h3>
                                            <p class="mb-0">إجمالي الأقسام</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-building fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['pending_requests']; ?></h3>
                                            <p class="mb-0">الطلبات المعلقة</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-clock fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['approved_requests']; ?></h3>
                                            <p class="mb-0">الطلبات المعتمدة هذا الشهر</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-check-circle fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php else: ?>
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['leave_balance']; ?></h3>
                                            <p class="mb-0">رصيد الإجازات</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-wallet2 fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['used_days']; ?></h3>
                                            <p class="mb-0">الأيام المستخدمة</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-calendar-x fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['remaining_days']; ?></h3>
                                            <p class="mb-0">الأيام المتبقية</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-calendar-check fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="col-xl-3 col-md-6 mb-4">
                            <div class="card stats-card">
                                <div class="card-body">
                                    <div class="d-flex justify-content-between">
                                        <div>
                                            <h3><?php echo $stats['my_requests']; ?></h3>
                                            <p class="mb-0">إجمالي طلباتي</p>
                                        </div>
                                        <div class="align-self-center">
                                            <i class="bi bi-list-ul fs-1"></i>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
                
                <!-- Recent Activity -->
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-clock-history me-2"></i>
                                    <?php echo hasRole('admin') ? 'طلبات الإجازة الحديثة' : 'طلباتي الحديثة'; ?>
                                </h5>
                            </div>
                            <div class="card-body">
                                <?php if (hasRole('admin') && !empty($recent_requests)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>رقم الطلب</th>
                                                    <th>الموظف</th>
                                                    <th>القسم</th>
                                                    <th>تاريخ البداية</th>
                                                    <th>تاريخ النهاية</th>
                                                    <th>الأيام</th>
                                                    <th>الحالة</th>
                                                    <th>الإجراءات</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($recent_requests as $request): ?>
                                                    <tr>
                                                        <td><?php echo preventXSS($request['request_number']); ?></td>
                                                        <td>
                                                            <?php echo preventXSS($request['full_name_ar']); ?>
                                                            <br><small class="text-muted"><?php echo preventXSS($request['employee_id']); ?></small>
                                                        </td>
                                                        <td><?php echo preventXSS($request['dept_name_ar']); ?></td>
                                                        <td><?php echo formatDate($request['start_date']); ?></td>
                                                        <td><?php echo formatDate($request['end_date']); ?></td>
                                                        <td><?php echo $request['total_days']; ?> يوم</td>
                                                        <td><?php echo getStatusBadge($request['status']); ?></td>
                                                        <td>
                                                            <a href="<?php echo BASE_URL; ?>/admin/leave-request-details.php?id=<?php echo $request['id']; ?>" 
                                                               class="btn btn-sm btn-outline-primary">
                                                                <i class="bi bi-eye"></i> عرض
                                                            </a>
                                                        </td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="<?php echo BASE_URL; ?>/admin/leave-requests.php" class="btn btn-primary">
                                            عرض جميع الطلبات
                                        </a>
                                    </div>
                                <?php elseif (!hasRole('admin') && !empty($my_requests)): ?>
                                    <div class="table-responsive">
                                        <table class="table table-hover">
                                            <thead>
                                                <tr>
                                                    <th>رقم الطلب</th>
                                                    <th>تاريخ البداية</th>
                                                    <th>تاريخ النهاية</th>
                                                    <th>الأيام</th>
                                                    <th>الحالة</th>
                                                    <th>تاريخ الطلب</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <?php foreach ($my_requests as $request): ?>
                                                    <tr>
                                                        <td><?php echo preventXSS($request['request_number']); ?></td>
                                                        <td><?php echo formatDate($request['start_date']); ?></td>
                                                        <td><?php echo formatDate($request['end_date']); ?></td>
                                                        <td><?php echo $request['total_days']; ?> يوم</td>
                                                        <td><?php echo getStatusBadge($request['status']); ?></td>
                                                        <td><?php echo formatDate($request['created_at']); ?></td>
                                                    </tr>
                                                <?php endforeach; ?>
                                            </tbody>
                                        </table>
                                    </div>
                                    <div class="text-center mt-3">
                                        <a href="<?php echo BASE_URL; ?>/leave/my-requests.php" class="btn btn-primary">
                                            عرض جميع طلباتي
                                        </a>
                                    </div>
                                <?php else: ?>
                                    <div class="text-center py-4">
                                        <i class="bi bi-inbox fs-1 text-muted"></i>
                                        <p class="text-muted mt-2">لا توجد طلبات حديثة</p>
                                        <?php if (!hasRole('admin')): ?>
                                            <a href="<?php echo BASE_URL; ?>/leave/request.php" class="btn btn-primary">
                                                <i class="bi bi-plus-circle me-2"></i>
                                                تقديم طلب إجازة جديد
                                            </a>
                                        <?php endif; ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Quick Actions -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header">
                                <h5 class="mb-0">
                                    <i class="bi bi-lightning me-2"></i>
                                    إجراءات سريعة
                                </h5>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <?php if (hasRole('admin')): ?>
                                        <div class="col-md-3 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/admin/departments.php" class="btn btn-outline-primary w-100">
                                                <i class="bi bi-building d-block fs-1 mb-2"></i>
                                                إدارة الأقسام
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/admin/users.php" class="btn btn-outline-success w-100">
                                                <i class="bi bi-people d-block fs-1 mb-2"></i>
                                                إدارة الموظفين
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/admin/leave-requests.php" class="btn btn-outline-warning w-100">
                                                <i class="bi bi-calendar-check d-block fs-1 mb-2"></i>
                                                مراجعة الطلبات
                                            </a>
                                        </div>
                                        <div class="col-md-3 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/admin/reports.php" class="btn btn-outline-info w-100">
                                                <i class="bi bi-graph-up d-block fs-1 mb-2"></i>
                                                التقارير
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <div class="col-md-4 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/leave/request.php" class="btn btn-outline-primary w-100">
                                                <i class="bi bi-plus-circle d-block fs-1 mb-2"></i>
                                                طلب إجازة جديد
                                            </a>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/leave/my-requests.php" class="btn btn-outline-success w-100">
                                                <i class="bi bi-list-ul d-block fs-1 mb-2"></i>
                                                طلباتي
                                            </a>
                                        </div>
                                        <div class="col-md-4 mb-3">
                                            <a href="<?php echo BASE_URL; ?>/leave/balance.php" class="btn btn-outline-info w-100">
                                                <i class="bi bi-wallet2 d-block fs-1 mb-2"></i>
                                                رصيد الإجازات
                                            </a>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
</div>

<?php include 'includes/footer.php'; ?>
